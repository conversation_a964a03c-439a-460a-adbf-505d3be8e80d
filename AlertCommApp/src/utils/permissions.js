// Role-based permissions system for AlertComm

export const ROLES = {
  ADMINISTRATOR: 'administrator',
  COMMANDER: 'commander',
  LEAD: 'lead',
  STAFF: 'staff',
  VOLUNTEER: 'volunteer'
};

export const PERMISSIONS = {
  // Event Management
  CREATE_EVENT: 'create_event',
  EDIT_EVENT: 'edit_event',
  DELETE_EVENT: 'delete_event',
  VIEW_ALL_EVENTS: 'view_all_events',
  ASSIGN_TASKS: 'assign_tasks',
  
  // User Management
  MANAGE_USERS: 'manage_users',
  VIEW_USER_PROFILES: 'view_user_profiles',
  ASSIGN_ROLES: 'assign_roles',
  
  // Communication
  SEND_BROADCASTS: 'send_broadcasts',
  ACCESS_ALL_CHATS: 'access_all_chats',
  MODERATE_CHAT: 'moderate_chat',
  
  // Resources
  MANAGE_RESOURCES: 'manage_resources',
  VIEW_RESOURCES: 'view_resources',
  ALLOCATE_RESOURCES: 'allocate_resources',
  
  // Reports
  GENERATE_REPORTS: 'generate_reports',
  VIEW_ANALYTICS: 'view_analytics',
  EXPORT_DATA: 'export_data',
  
  // System
  SYSTEM_SETTINGS: 'system_settings',
  VIEW_LOGS: 'view_logs',
  
  // Documents
  UPLOAD_DOCUMENTS: 'upload_documents',
  DELETE_DOCUMENTS: 'delete_documents',
  VIEW_DOCUMENTS: 'view_documents',
};

// Role-based permission mapping
const ROLE_PERMISSIONS = {
  [ROLES.ADMINISTRATOR]: [
    // Full system access - same as commander plus additional admin privileges
    PERMISSIONS.CREATE_EVENT,
    PERMISSIONS.EDIT_EVENT,
    PERMISSIONS.DELETE_EVENT,
    PERMISSIONS.VIEW_ALL_EVENTS,
    PERMISSIONS.ASSIGN_TASKS,
    PERMISSIONS.MANAGE_USERS,
    PERMISSIONS.VIEW_USER_PROFILES,
    PERMISSIONS.ASSIGN_ROLES,
    PERMISSIONS.SEND_BROADCASTS,
    PERMISSIONS.ACCESS_ALL_CHATS,
    PERMISSIONS.MODERATE_CHAT,
    PERMISSIONS.MANAGE_RESOURCES,
    PERMISSIONS.VIEW_RESOURCES,
    PERMISSIONS.ALLOCATE_RESOURCES,
    PERMISSIONS.GENERATE_REPORTS,
    PERMISSIONS.VIEW_ANALYTICS,
    PERMISSIONS.EXPORT_DATA,
    PERMISSIONS.SYSTEM_SETTINGS,
    PERMISSIONS.VIEW_LOGS,
    PERMISSIONS.UPLOAD_DOCUMENTS,
    PERMISSIONS.DELETE_DOCUMENTS,
    PERMISSIONS.VIEW_DOCUMENTS,
  ],

  [ROLES.COMMANDER]: [
    // Full access to everything
    PERMISSIONS.CREATE_EVENT,
    PERMISSIONS.EDIT_EVENT,
    PERMISSIONS.DELETE_EVENT,
    PERMISSIONS.VIEW_ALL_EVENTS,
    PERMISSIONS.ASSIGN_TASKS,
    PERMISSIONS.MANAGE_USERS,
    PERMISSIONS.VIEW_USER_PROFILES,
    PERMISSIONS.ASSIGN_ROLES,
    PERMISSIONS.SEND_BROADCASTS,
    PERMISSIONS.ACCESS_ALL_CHATS,
    PERMISSIONS.MODERATE_CHAT,
    PERMISSIONS.MANAGE_RESOURCES,
    PERMISSIONS.VIEW_RESOURCES,
    PERMISSIONS.ALLOCATE_RESOURCES,
    PERMISSIONS.GENERATE_REPORTS,
    PERMISSIONS.VIEW_ANALYTICS,
    PERMISSIONS.EXPORT_DATA,
    PERMISSIONS.SYSTEM_SETTINGS,
    PERMISSIONS.VIEW_LOGS,
    PERMISSIONS.UPLOAD_DOCUMENTS,
    PERMISSIONS.DELETE_DOCUMENTS,
    PERMISSIONS.VIEW_DOCUMENTS,
  ],
  
  [ROLES.LEAD]: [
    // Limited management capabilities
    PERMISSIONS.CREATE_EVENT,
    PERMISSIONS.EDIT_EVENT,
    PERMISSIONS.VIEW_ALL_EVENTS,
    PERMISSIONS.ASSIGN_TASKS,
    PERMISSIONS.VIEW_USER_PROFILES,
    PERMISSIONS.SEND_BROADCASTS,
    PERMISSIONS.ACCESS_ALL_CHATS,
    PERMISSIONS.MODERATE_CHAT,
    PERMISSIONS.VIEW_RESOURCES,
    PERMISSIONS.ALLOCATE_RESOURCES,
    PERMISSIONS.GENERATE_REPORTS,
    PERMISSIONS.VIEW_ANALYTICS,
    PERMISSIONS.UPLOAD_DOCUMENTS,
    PERMISSIONS.VIEW_DOCUMENTS,
  ],
  
  [ROLES.STAFF]: [
    // Operational access
    PERMISSIONS.VIEW_ALL_EVENTS,
    PERMISSIONS.VIEW_USER_PROFILES,
    PERMISSIONS.ACCESS_ALL_CHATS,
    PERMISSIONS.VIEW_RESOURCES,
    PERMISSIONS.VIEW_ANALYTICS,
    PERMISSIONS.UPLOAD_DOCUMENTS,
    PERMISSIONS.VIEW_DOCUMENTS,
  ],
  
  [ROLES.VOLUNTEER]: [
    // Basic access
    PERMISSIONS.VIEW_ALL_EVENTS,
    PERMISSIONS.ACCESS_ALL_CHATS,
    PERMISSIONS.VIEW_RESOURCES,
    PERMISSIONS.VIEW_DOCUMENTS,
  ],
};

/**
 * Check if a user has a specific permission
 * @param {string} userRole - The user's role
 * @param {string} permission - The permission to check
 * @returns {boolean} - Whether the user has the permission
 */
export const hasPermission = (userRole, permission) => {
  if (!userRole || !permission) return false;
  
  const rolePermissions = ROLE_PERMISSIONS[userRole.toLowerCase()] || [];
  return rolePermissions.includes(permission);
};

/**
 * Check if a user has any of the specified permissions
 * @param {string} userRole - The user's role
 * @param {string[]} permissions - Array of permissions to check
 * @returns {boolean} - Whether the user has any of the permissions
 */
export const hasAnyPermission = (userRole, permissions) => {
  if (!userRole || !permissions || !Array.isArray(permissions)) return false;
  
  return permissions.some(permission => hasPermission(userRole, permission));
};

/**
 * Check if a user has all of the specified permissions
 * @param {string} userRole - The user's role
 * @param {string[]} permissions - Array of permissions to check
 * @returns {boolean} - Whether the user has all of the permissions
 */
export const hasAllPermissions = (userRole, permissions) => {
  if (!userRole || !permissions || !Array.isArray(permissions)) return false;
  
  return permissions.every(permission => hasPermission(userRole, permission));
};

/**
 * Get all permissions for a role
 * @param {string} userRole - The user's role
 * @returns {string[]} - Array of permissions for the role
 */
export const getRolePermissions = (userRole) => {
  if (!userRole) return [];
  
  return ROLE_PERMISSIONS[userRole.toLowerCase()] || [];
};

/**
 * Check if a role is valid
 * @param {string} role - The role to check
 * @returns {boolean} - Whether the role is valid
 */
export const isValidRole = (role) => {
  return Object.values(ROLES).includes(role?.toLowerCase());
};

/**
 * Get role display name
 * @param {string} role - The role
 * @returns {string} - Formatted role name
 */
export const getRoleDisplayName = (role) => {
  if (!role) return 'Unknown';
  
  const roleMap = {
    [ROLES.ADMINISTRATOR]: 'Administrator',
    [ROLES.COMMANDER]: 'Commander',
    [ROLES.LEAD]: 'Team Lead',
    [ROLES.STAFF]: 'Staff',
    [ROLES.VOLUNTEER]: 'Volunteer'
  };
  
  return roleMap[role.toLowerCase()] || role.charAt(0).toUpperCase() + role.slice(1);
};

export default {
  ROLES,
  PERMISSIONS,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  getRolePermissions,
  isValidRole,
  getRoleDisplayName
};
