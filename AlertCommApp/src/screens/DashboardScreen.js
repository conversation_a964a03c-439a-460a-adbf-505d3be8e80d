import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { useNotifications } from '../context/NotificationContext';
import apiService from '../services/api';
import socketService from '../services/socket';
import config from '../config/config';

const DashboardScreen = ({ navigation }) => {
  const { user } = useAuth();
  const { showEmergencyNotification } = useNotifications();
  const [events, setEvents] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [responderStatus, setResponderStatus] = useState('available');
  const [userProfile, setUserProfile] = useState(null);

  useEffect(() => {
    loadDashboardData();
    setupSocketListeners();
    
    return () => {
      socketService.off('newEvent', handleNewEvent);
      socketService.off('statusUpdate', handleStatusUpdate);
    };
  }, []);

  const loadDashboardData = async () => {
    try {
      const [eventsData, profileData] = await Promise.all([
        apiService.getEvents(),
        apiService.getUserProfile(),
      ]);

      setEvents(eventsData.filter(event => event.status === 'open'));
      setUserProfile(profileData);
      setResponderStatus(profileData.status || 'available');
      
      // Connect to socket after loading data
      await socketService.connect();
      socketService.joinUserRoom(user.id);
      
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const setupSocketListeners = () => {
    socketService.on('newEvent', handleNewEvent);
    socketService.on('statusUpdate', handleStatusUpdate);
  };

  const handleNewEvent = (event) => {
    setEvents(prev => [event, ...prev]);
    showEmergencyNotification(event);
  };

  const handleStatusUpdate = (update) => {
    if (update.userId === user.id) {
      setResponderStatus(update.status);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const updateStatus = async (newStatus) => {
    try {
      await apiService.updateResponderStatus(newStatus);
      setResponderStatus(newStatus);
      socketService.sendStatusUpdate(null, user.id, newStatus);
    } catch (error) {
      Alert.alert('Error', 'Failed to update status');
    }
  };

  const getStatusColor = (status) => {
    return config.STATUS_COLORS[status] || config.COLORS.disabled;
  };

  const getUrgencyColor = (urgency) => {
    return config.URGENCY_COLORS[urgency] || config.COLORS.textSecondary;
  };

  const renderStatusCard = () => (
    <View style={styles.statusCard}>
      <Text style={styles.cardTitle}>Quick Status Update</Text>
      <View style={styles.statusButtons}>
        {config.RESPONSE_STATUS.slice(0, 4).map((status) => (
          <TouchableOpacity
            key={status.value}
            style={[
              styles.statusButton,
              { backgroundColor: status.color },
              responderStatus === status.value && styles.activeStatusButton
            ]}
            onPress={() => updateStatus(status.value)}
          >
            <Text style={styles.statusButtonText}>{status.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderActiveEvents = () => (
    <View style={styles.eventsCard}>
      <View style={styles.cardHeader}>
        <Text style={styles.cardTitle}>Active Events</Text>
        <View style={styles.eventCountBadge}>
          <Text style={styles.eventCountText}>{events.length}</Text>
        </View>
        <TouchableOpacity onPress={() => navigation.navigate('Events')}>
          <Text style={styles.viewAllText}>View All</Text>
        </TouchableOpacity>
      </View>
      {events.length === 0 ? (
        <View style={styles.noEventsContainer}>
          <Ionicons name="checkmark-circle" size={48} color={config.COLORS.success} />
          <Text style={styles.noEventsText}>No active events</Text>
          <Text style={styles.noEventsSubtext}>All clear - no emergency responses needed</Text>
        </View>
      ) : (
        events.map((event) => (
          <TouchableOpacity
            key={event.id}
            style={styles.eventItem}
            onPress={() => navigation.navigate('EventDetail', { eventId: event.id })}
          >
            <View style={styles.eventHeader}>
              <Text style={styles.eventTitle} numberOfLines={1}>{event.title}</Text>
              <View style={[styles.urgencyBadge, { backgroundColor: getUrgencyColor(event.urgency) }]}>
                <Text style={styles.urgencyText}>{event.urgency}</Text>
              </View>
            </View>
            <Text style={styles.eventInfo} numberOfLines={2}>
              {event.info || event.description}
            </Text>
            <View style={styles.eventFooter}>
              <View style={styles.eventLocation}>
                <Ionicons name="location" size={14} color={config.COLORS.textSecondary} />
                <Text style={styles.locationText} numberOfLines={1}>
                  {event.location?.address || event.location || 'Location not specified'}
                </Text>
              </View>
              <View style={styles.eventResponders}>
                <Ionicons name="people" size={14} color={config.COLORS.textSecondary} />
                <Text style={styles.respondersText}>
                  {event.assigned_count || 0} responders
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        ))
      )}
    </View>
  );

  const renderWelcomeHeader = () => {
    const currentHour = new Date().getHours();
    let greeting = 'Good morning';
    if (currentHour >= 12 && currentHour < 17) {
      greeting = 'Good afternoon';
    } else if (currentHour >= 17) {
      greeting = 'Good evening';
    }

    const displayName = userProfile?.firstName && userProfile?.lastName
      ? `${userProfile.firstName} ${userProfile.lastName}`
      : user.username;

    const roleDisplay = user.role?.charAt(0).toUpperCase() + user.role?.slice(1) || 'Staff';

    return (
      <View style={styles.welcomeHeader}>
        <View style={styles.welcomeContent}>
          <Text style={styles.greetingText}>{greeting},</Text>
          <Text style={styles.userDisplayName}>{displayName}</Text>
          <View style={styles.userInfoRow}>
            <View style={styles.roleContainer}>
              <Ionicons name="person-circle" size={16} color={config.COLORS.primary} />
              <Text style={styles.roleText}>{roleDisplay}</Text>
            </View>
            {userProfile?.mainLocation && (
              <View style={styles.locationContainer}>
                <Ionicons name="location" size={16} color={config.COLORS.textSecondary} />
                <Text style={styles.locationText}>{userProfile.mainLocation}</Text>
              </View>
            )}
          </View>
          <View style={styles.statusRow}>
            <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(responderStatus) }]} />
            <Text style={styles.statusLabel}>Status: {responderStatus.charAt(0).toUpperCase() + responderStatus.slice(1)}</Text>
            <Text style={styles.lastUpdated}>• {new Date().toLocaleDateString()}</Text>
          </View>
        </View>
        <View style={styles.welcomeIcon}>
          <Ionicons name="shield-checkmark" size={32} color={config.COLORS.primary} />
        </View>
      </View>
    );
  };

  const renderQuickActions = () => (
    <View style={styles.quickActionsCard}>
      <Text style={styles.cardTitle}>Quick Actions</Text>
      <View style={styles.quickActionsGrid}>
        <TouchableOpacity 
          style={styles.quickActionButton}
          onPress={() => navigation.navigate('Events')}
        >
          <Ionicons name="alert-circle" size={24} color={config.COLORS.primary} />
          <Text style={styles.quickActionText}>View Events</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.quickActionButton}
          onPress={() => updateStatus('available')}
        >
          <Ionicons name="checkmark-circle" size={24} color={config.COLORS.success} />
          <Text style={styles.quickActionText}>Available</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.quickActionButton}
          onPress={() => updateStatus('unavailable')}
        >
          <Ionicons name="close-circle" size={24} color={config.COLORS.error} />
          <Text style={styles.quickActionText}>Unavailable</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading dashboard...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {renderWelcomeHeader()}
      {renderStatusCard()}
      {renderActiveEvents()}
      {renderQuickActions()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  welcomeHeader: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginBottom: 8,
    padding: 20,
    borderRadius: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  welcomeContent: {
    flex: 1,
  },
  greetingText: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
    marginBottom: 4,
  },
  userDisplayName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: config.COLORS.text,
    marginBottom: 8,
  },
  userInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    flexWrap: 'wrap',
  },
  roleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  roleText: {
    fontSize: 14,
    color: config.COLORS.primary,
    fontWeight: '600',
    marginLeft: 4,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    marginLeft: 4,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusLabel: {
    fontSize: 14,
    color: config.COLORS.text,
    fontWeight: '500',
  },
  lastUpdated: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
    marginLeft: 8,
  },
  welcomeIcon: {
    marginLeft: 16,
  },
  statusCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: config.COLORS.text,
    marginBottom: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: config.COLORS.text,
  },
  statusButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusButton: {
    flex: 1,
    padding: 8,
    borderRadius: 8,
    marginHorizontal: 2,
    alignItems: 'center',
  },
  activeStatusButton: {
    borderWidth: 2,
    borderColor: config.COLORS.text,
  },
  statusButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  eventsCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  eventCountBadge: {
    backgroundColor: config.COLORS.primary,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginLeft: 8,
  },
  eventCountText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  viewAllText: {
    color: config.COLORS.primary,
    fontWeight: 'bold',
  },
  noEventsContainer: {
    alignItems: 'center',
    padding: 32,
  },
  noEventsText: {
    textAlign: 'center',
    color: config.COLORS.text,
    fontSize: 16,
    fontWeight: '600',
    marginTop: 12,
  },
  noEventsSubtext: {
    textAlign: 'center',
    color: config.COLORS.textSecondary,
    fontSize: 14,
    marginTop: 4,
  },
  eventItem: {
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
    paddingVertical: 12,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: config.COLORS.text,
    flex: 1,
  },
  urgencyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  urgencyText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  eventInfo: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    marginBottom: 8,
  },
  eventFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  eventLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 16,
  },
  locationText: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
    marginLeft: 4,
  },
  eventResponders: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  respondersText: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
    marginLeft: 4,
  },
  quickActionsCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    width: '48%',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    backgroundColor: config.COLORS.background,
    marginBottom: 8,
  },
  quickActionText: {
    marginTop: 8,
    fontSize: 12,
    color: config.COLORS.text,
    textAlign: 'center',
  },
});

export default DashboardScreen;
