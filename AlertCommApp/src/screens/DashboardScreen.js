import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import { useNotifications } from '../context/NotificationContext';
import apiService from '../services/api';
import socketService from '../services/socket';
import config from '../config/config';

const DashboardScreen = ({ navigation }) => {
  const { user } = useAuth();
  const { showEmergencyNotification } = useNotifications();
  const [events, setEvents] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [responderStatus, setResponderStatus] = useState('available');
  const [userProfile, setUserProfile] = useState(null);

  useEffect(() => {
    loadDashboardData();
    setupSocketListeners();
    
    return () => {
      socketService.off('newEvent', handleNewEvent);
      socketService.off('statusUpdate', handleStatusUpdate);
    };
  }, []);

  const loadDashboardData = async () => {
    try {
      const [eventsData, profileData] = await Promise.all([
        apiService.getEvents(),
        apiService.getUserProfile(),
      ]);

      setEvents(eventsData.filter(event => event.status === 'open'));
      setUserProfile(profileData);
      setResponderStatus(profileData.status || 'available');
      
      // Connect to socket after loading data
      await socketService.connect();
      socketService.joinUserRoom(user.id);
      
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const setupSocketListeners = () => {
    socketService.on('newEvent', handleNewEvent);
    socketService.on('statusUpdate', handleStatusUpdate);
  };

  const handleNewEvent = (event) => {
    setEvents(prev => [event, ...prev]);
    showEmergencyNotification(event);
  };

  const handleStatusUpdate = (update) => {
    if (update.userId === user.id) {
      setResponderStatus(update.status);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const updateStatus = async (newStatus) => {
    try {
      await apiService.updateResponderStatus(newStatus);
      setResponderStatus(newStatus);
      socketService.sendStatusUpdate(null, user.id, newStatus);
    } catch (error) {
      Alert.alert('Error', 'Failed to update status');
    }
  };

  const getStatusColor = (status) => {
    return config.STATUS_COLORS[status] || config.COLORS.disabled;
  };

  const getUrgencyColor = (urgency) => {
    return config.URGENCY_COLORS[urgency] || config.COLORS.textSecondary;
  };

  const renderStatusCard = () => (
    <View style={styles.statusCard}>
      <Text style={styles.cardTitle}>Your Status</Text>
      <View style={styles.statusContainer}>
        <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(responderStatus) }]} />
        <Text style={styles.statusText}>{responderStatus.toUpperCase()}</Text>
      </View>
      <View style={styles.statusButtons}>
        {config.RESPONSE_STATUS.slice(0, 4).map((status) => (
          <TouchableOpacity
            key={status.value}
            style={[
              styles.statusButton,
              { backgroundColor: status.color },
              responderStatus === status.value && styles.activeStatusButton
            ]}
            onPress={() => updateStatus(status.value)}
          >
            <Text style={styles.statusButtonText}>{status.label}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderActiveEvents = () => (
    <View style={styles.eventsCard}>
      <View style={styles.cardHeader}>
        <Text style={styles.cardTitle}>Active Events</Text>
        <TouchableOpacity onPress={() => navigation.navigate('Events')}>
          <Text style={styles.viewAllText}>View All</Text>
        </TouchableOpacity>
      </View>
      {events.length === 0 ? (
        <Text style={styles.noEventsText}>No active events</Text>
      ) : (
        events.slice(0, 3).map((event) => (
          <TouchableOpacity
            key={event.id}
            style={styles.eventItem}
            onPress={() => navigation.navigate('EventDetail', { eventId: event.id })}
          >
            <View style={styles.eventHeader}>
              <Text style={styles.eventTitle}>{event.title}</Text>
              <View style={[styles.urgencyBadge, { backgroundColor: getUrgencyColor(event.urgency) }]}>
                <Text style={styles.urgencyText}>{event.urgency}</Text>
              </View>
            </View>
            <Text style={styles.eventInfo} numberOfLines={2}>
              {event.info || event.description}
            </Text>
            <Text style={styles.eventTime}>
              {new Date(event.created_at).toLocaleTimeString()}
            </Text>
          </TouchableOpacity>
        ))
      )}
    </View>
  );

  const renderWelcomeHeader = () => {
    const currentHour = new Date().getHours();
    let greeting = 'Good morning';
    if (currentHour >= 12 && currentHour < 17) {
      greeting = 'Good afternoon';
    } else if (currentHour >= 17) {
      greeting = 'Good evening';
    }

    const displayName = userProfile?.firstName && userProfile?.lastName
      ? `${userProfile.firstName} ${userProfile.lastName}`
      : user.username;

    const roleDisplay = user.role?.charAt(0).toUpperCase() + user.role?.slice(1) || 'Staff';

    return (
      <View style={styles.welcomeHeader}>
        <View style={styles.welcomeContent}>
          <Text style={styles.greetingText}>{greeting},</Text>
          <Text style={styles.userDisplayName}>{displayName}</Text>
          <View style={styles.userInfoRow}>
            <View style={styles.roleContainer}>
              <Ionicons name="person-circle" size={16} color={config.COLORS.primary} />
              <Text style={styles.roleText}>{roleDisplay}</Text>
            </View>
            {userProfile?.mainLocation && (
              <View style={styles.locationContainer}>
                <Ionicons name="location" size={16} color={config.COLORS.textSecondary} />
                <Text style={styles.locationText}>{userProfile.mainLocation}</Text>
              </View>
            )}
          </View>
          <View style={styles.statusRow}>
            <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(responderStatus) }]} />
            <Text style={styles.statusLabel}>Status: {responderStatus.charAt(0).toUpperCase() + responderStatus.slice(1)}</Text>
            <Text style={styles.lastUpdated}>• {new Date().toLocaleDateString()}</Text>
          </View>
        </View>
        <View style={styles.welcomeIcon}>
          <Ionicons name="shield-checkmark" size={32} color={config.COLORS.primary} />
        </View>
      </View>
    );
  };

  const renderQuickActions = () => (
    <View style={styles.quickActionsCard}>
      <Text style={styles.cardTitle}>Quick Actions</Text>
      <View style={styles.quickActionsGrid}>
        <TouchableOpacity 
          style={styles.quickActionButton}
          onPress={() => navigation.navigate('Events')}
        >
          <Ionicons name="alert-circle" size={24} color={config.COLORS.primary} />
          <Text style={styles.quickActionText}>View Events</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.quickActionButton}
          onPress={() => navigation.navigate('Chat')}
        >
          <Ionicons name="chatbubbles" size={24} color={config.COLORS.primary} />
          <Text style={styles.quickActionText}>Team Chat</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.quickActionButton}
          onPress={() => updateStatus('available')}
        >
          <Ionicons name="checkmark-circle" size={24} color={config.COLORS.success} />
          <Text style={styles.quickActionText}>Available</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.quickActionButton}
          onPress={() => updateStatus('unavailable')}
        >
          <Ionicons name="close-circle" size={24} color={config.COLORS.error} />
          <Text style={styles.quickActionText}>Unavailable</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading dashboard...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.welcomeText}>Welcome back,</Text>
        <Text style={styles.userName}>{user.username}</Text>
      </View>

      {renderStatusCard()}
      {renderActiveEvents()}
      {renderQuickActions()}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    padding: 20,
    backgroundColor: config.COLORS.surface,
  },
  welcomeText: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: config.COLORS.text,
  },
  statusCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: config.COLORS.text,
    marginBottom: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: config.COLORS.text,
  },
  statusButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusButton: {
    flex: 1,
    padding: 8,
    borderRadius: 8,
    marginHorizontal: 2,
    alignItems: 'center',
  },
  activeStatusButton: {
    borderWidth: 2,
    borderColor: config.COLORS.text,
  },
  statusButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  eventsCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  viewAllText: {
    color: config.COLORS.primary,
    fontWeight: 'bold',
  },
  noEventsText: {
    textAlign: 'center',
    color: config.COLORS.textSecondary,
    fontStyle: 'italic',
    padding: 20,
  },
  eventItem: {
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
    paddingVertical: 12,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: config.COLORS.text,
    flex: 1,
  },
  urgencyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  urgencyText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  eventInfo: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    marginBottom: 4,
  },
  eventTime: {
    fontSize: 12,
    color: config.COLORS.disabled,
  },
  quickActionsCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    width: '48%',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    backgroundColor: config.COLORS.background,
    marginBottom: 8,
  },
  quickActionText: {
    marginTop: 8,
    fontSize: 12,
    color: config.COLORS.text,
    textAlign: 'center',
  },
});

export default DashboardScreen;
