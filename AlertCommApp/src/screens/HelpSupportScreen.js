import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Linking,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import config from '../config/config';

const HelpSupportScreen = ({ navigation }) => {
  const [expandedFAQ, setExpandedFAQ] = useState(null);

  const handleLinkPress = (url) => {
    Linking.openURL(url).catch(err => console.error('Failed to open URL:', err));
  };

  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      'Choose how you would like to contact our support team:',
      [
        {
          text: 'Email',
          onPress: () => handleLinkPress('mailto:<EMAIL>?subject=AlertComm Support Request')
        },
        {
          text: 'Phone',
          onPress: () => handleLinkPress('tel:+18002537801')
        },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const faqData = [
    {
      id: 1,
      question: 'How do I respond to an emergency assignment?',
      answer: 'When you receive an assignment notification, tap on it to view details. You can then choose to Accept, Decline, or mark yourself as En Route. Your status will be updated in real-time for the command team.'
    },
    {
      id: 2,
      question: 'How do I update my location during an emergency?',
      answer: 'Your location is automatically shared when you accept an assignment. You can manually update your status (En Route, On Scene, Completed) which will update your position on the command map.'
    },
    {
      id: 3,
      question: 'Can I see other responders\' locations?',
      answer: 'Yes, when assigned to an event, you can see the locations of other responders assigned to the same event through the event map view. This helps with coordination and situational awareness.'
    },
    {
      id: 4,
      question: 'How do I use the chat features?',
      answer: 'There are two chat types: Event Team Chat (private to assigned responders) and Global Team Chat (all team members). Access event chat from the event details page, or use the floating chat button for global communication.'
    },
    {
      id: 5,
      question: 'What should I do if I don\'t receive notifications?',
      answer: 'Check your notification settings in the Profile tab. Ensure notifications are enabled for the app in your device settings. If issues persist, contact your administrator or support.'
    },
    {
      id: 6,
      question: 'How do I access event documents?',
      answer: 'Event documents are available in the event details page. You can view, download, and upload documents related to the specific emergency event you\'re assigned to.'
    },
    {
      id: 7,
      question: 'Can I work offline?',
      answer: 'Basic functionality works offline, but real-time features like chat, notifications, and location updates require an internet connection. Data will sync when connection is restored.'
    },
    {
      id: 8,
      question: 'How do I change my availability status?',
      answer: 'Your availability can be updated in the Profile section. This helps commanders know who is available for assignment during emergency situations.'
    }
  ];

  const toggleFAQ = (id) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };

  const renderFAQItem = (item) => (
    <View key={item.id} style={styles.faqItem}>
      <TouchableOpacity
        style={styles.faqQuestion}
        onPress={() => toggleFAQ(item.id)}
      >
        <Text style={styles.faqQuestionText}>{item.question}</Text>
        <Ionicons
          name={expandedFAQ === item.id ? 'chevron-up' : 'chevron-down'}
          size={20}
          color={config.COLORS.textSecondary}
        />
      </TouchableOpacity>
      {expandedFAQ === item.id && (
        <View style={styles.faqAnswer}>
          <Text style={styles.faqAnswerText}>{item.answer}</Text>
        </View>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={config.COLORS.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Help & Support</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={true}
        keyboardShouldPersistTaps="handled"
        removeClippedSubviews={false}
        scrollEventThrottle={16}
      >
        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <TouchableOpacity style={styles.actionCard} onPress={handleContactSupport}>
            <View style={styles.actionIcon}>
              <Ionicons name="headset" size={24} color={config.COLORS.primary} />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Contact Support</Text>
              <Text style={styles.actionDescription}>Get help from our support team</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={config.COLORS.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.actionCard}
            onPress={() => handleLinkPress('https://docs.alertcomm.com')}
          >
            <View style={styles.actionIcon}>
              <Ionicons name="book" size={24} color={config.COLORS.primary} />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>User Manual</Text>
              <Text style={styles.actionDescription}>Complete guide to using AlertComm</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={config.COLORS.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity 
            style={styles.actionCard}
            onPress={() => handleLinkPress('https://training.alertcomm.com')}
          >
            <View style={styles.actionIcon}>
              <Ionicons name="school" size={24} color={config.COLORS.primary} />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>Training Videos</Text>
              <Text style={styles.actionDescription}>Watch tutorials and training materials</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={config.COLORS.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Emergency Contacts */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Emergency Contacts</Text>
          <View style={styles.emergencyCard}>
            <View style={styles.emergencyHeader}>
              <Ionicons name="warning" size={24} color="#f44336" />
              <Text style={styles.emergencyTitle}>For Life-Threatening Emergencies</Text>
            </View>
            <Text style={styles.emergencyText}>
              Always call 911 first for immediate life-threatening emergencies. 
              AlertComm is for coordination, not emergency dispatch.
            </Text>
          </View>

          <View style={styles.contactGrid}>
            <TouchableOpacity 
              style={styles.contactCard}
              onPress={() => handleLinkPress('tel:911')}
            >
              <Ionicons name="call" size={32} color="#f44336" />
              <Text style={styles.contactTitle}>911</Text>
              <Text style={styles.contactSubtitle}>Emergency Services</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.contactCard}
              onPress={() => handleLinkPress('tel:+18002537801')}
            >
              <Ionicons name="headset" size={32} color={config.COLORS.primary} />
              <Text style={styles.contactTitle}>Support</Text>
              <Text style={styles.contactSubtitle}>1-800-ALERT-01</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* FAQ Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
          {faqData.map(renderFAQItem)}
        </View>

        {/* System Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>System Status</Text>
          <View style={styles.statusCard}>
            <View style={styles.statusItem}>
              <View style={[styles.statusIndicator, { backgroundColor: '#4caf50' }]} />
              <Text style={styles.statusText}>All Systems Operational</Text>
            </View>
            <TouchableOpacity 
              onPress={() => handleLinkPress('https://status.alertcomm.com')}
            >
              <Text style={styles.statusLink}>View Detailed Status</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Feedback */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Feedback</Text>
          <Text style={styles.paragraph}>
            Help us improve AlertComm by sharing your feedback and suggestions.
          </Text>
          <TouchableOpacity 
            style={styles.feedbackButton}
            onPress={() => handleLinkPress('mailto:<EMAIL>?subject=AlertComm Feedback')}
          >
            <Ionicons name="chatbubble-ellipses" size={20} color="#fff" />
            <Text style={styles.feedbackButtonText}>Send Feedback</Text>
          </TouchableOpacity>
        </View>

        {/* App Info */}
        <View style={styles.appInfoSection}>
          <Text style={styles.appInfoTitle}>AlertComm v1.0.0</Text>
          <Text style={styles.appInfoText}>
            Emergency Response Coordination Platform
          </Text>
          <Text style={styles.appInfoText}>
            © 2024 AlertComm. All rights reserved.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
    backgroundColor: config.COLORS.surface,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  section: {
    padding: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 16,
  },
  paragraph: {
    fontSize: 16,
    color: config.COLORS.text,
    lineHeight: 24,
    marginBottom: 16,
  },
  actionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: config.COLORS.surface,
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: config.COLORS.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 2,
  },
  actionDescription: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
  },
  emergencyCard: {
    backgroundColor: '#ffebee',
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#f44336',
    marginBottom: 16,
  },
  emergencyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  emergencyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#d32f2f',
    marginLeft: 8,
  },
  emergencyText: {
    fontSize: 14,
    color: '#d32f2f',
    lineHeight: 20,
  },
  contactGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  contactCard: {
    alignItems: 'center',
    backgroundColor: config.COLORS.surface,
    padding: 20,
    borderRadius: 8,
    flex: 0.45,
  },
  contactTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: config.COLORS.text,
    marginTop: 8,
  },
  contactSubtitle: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
    marginTop: 2,
  },
  faqItem: {
    backgroundColor: config.COLORS.surface,
    borderRadius: 8,
    marginBottom: 8,
    overflow: 'hidden',
  },
  faqQuestion: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  faqQuestionText: {
    fontSize: 16,
    fontWeight: '500',
    color: config.COLORS.text,
    flex: 1,
    marginRight: 8,
  },
  faqAnswer: {
    padding: 16,
    paddingTop: 0,
    borderTopWidth: 1,
    borderTopColor: config.COLORS.border,
  },
  faqAnswerText: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    lineHeight: 20,
  },
  statusCard: {
    backgroundColor: config.COLORS.surface,
    padding: 16,
    borderRadius: 8,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    color: config.COLORS.text,
    fontWeight: '500',
  },
  statusLink: {
    fontSize: 14,
    color: config.COLORS.primary,
    textDecorationLine: 'underline',
  },
  feedbackButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: config.COLORS.primary,
    padding: 12,
    borderRadius: 8,
  },
  feedbackButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  appInfoSection: {
    alignItems: 'center',
    padding: 24,
    backgroundColor: config.COLORS.surface,
    marginTop: 16,
  },
  appInfoTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 4,
  },
  appInfoText: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: 2,
  },
});

export default HelpSupportScreen;
