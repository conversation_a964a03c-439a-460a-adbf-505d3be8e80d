import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Image,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import config from '../config/config';

const AboutScreen = ({ navigation }) => {
  const handleLinkPress = (url) => {
    Linking.openURL(url).catch(err => console.error('Failed to open URL:', err));
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={config.COLORS.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>About AlertComm</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={true}
        keyboardShouldPersistTaps="handled"
        removeClippedSubviews={false}
        scrollEventThrottle={16}
      >
        {/* App Logo and Title */}
        <View style={styles.logoSection}>
          <View style={styles.logoContainer}>
            <Ionicons name="shield-checkmark" size={64} color={config.COLORS.primary} />
          </View>
          <Text style={styles.appName}>AlertComm</Text>
          <Text style={styles.appTagline}>Emergency Response Coordination Platform</Text>
          <Text style={styles.version}>Version 1.0.0</Text>
        </View>

        {/* Mission Statement */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Our Mission</Text>
          <Text style={styles.paragraph}>
            AlertComm is dedicated to revolutionizing emergency response coordination by providing 
            real-time communication, location tracking, and resource management tools for first 
            responders, emergency personnel, and volunteer organizations.
          </Text>
        </View>

        {/* Key Features */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Key Features</Text>
          
          <View style={styles.featureItem}>
            <Ionicons name="notifications" size={24} color={config.COLORS.primary} />
            <View style={styles.featureText}>
              <Text style={styles.featureTitle}>Real-time Notifications</Text>
              <Text style={styles.featureDescription}>
                Instant alerts and assignment notifications for emergency situations
              </Text>
            </View>
          </View>

          <View style={styles.featureItem}>
            <Ionicons name="location" size={24} color={config.COLORS.primary} />
            <View style={styles.featureText}>
              <Text style={styles.featureTitle}>Location Tracking</Text>
              <Text style={styles.featureDescription}>
                GPS-based responder tracking and event location mapping
              </Text>
            </View>
          </View>

          <View style={styles.featureItem}>
            <Ionicons name="chatbubbles" size={24} color={config.COLORS.primary} />
            <View style={styles.featureText}>
              <Text style={styles.featureTitle}>Team Communication</Text>
              <Text style={styles.featureDescription}>
                Secure messaging for event coordination and general team communication
              </Text>
            </View>
          </View>

          <View style={styles.featureItem}>
            <Ionicons name="people" size={24} color={config.COLORS.primary} />
            <View style={styles.featureText}>
              <Text style={styles.featureTitle}>Role-based Access</Text>
              <Text style={styles.featureDescription}>
                Customized interfaces for commanders, leads, staff, and volunteers
              </Text>
            </View>
          </View>

          <View style={styles.featureItem}>
            <Ionicons name="document-text" size={24} color={config.COLORS.primary} />
            <View style={styles.featureText}>
              <Text style={styles.featureTitle}>Document Management</Text>
              <Text style={styles.featureDescription}>
                Secure sharing of emergency plans, photos, and critical documents
              </Text>
            </View>
          </View>
        </View>

        {/* Technology */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Technology</Text>
          <Text style={styles.paragraph}>
            Built with modern technologies to ensure reliability and performance:
          </Text>
          <Text style={styles.bulletPoint}>• React Native for cross-platform compatibility</Text>
          <Text style={styles.bulletPoint}>• Real-time WebSocket communication</Text>
          <Text style={styles.bulletPoint}>• Secure encrypted data transmission</Text>
          <Text style={styles.bulletPoint}>• Cloud-based infrastructure for scalability</Text>
          <Text style={styles.bulletPoint}>• Offline capability for critical functions</Text>
        </View>

        {/* Company Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Company Information</Text>
          <Text style={styles.paragraph}>
            AlertComm is developed by a team of emergency response professionals and 
            technology experts committed to improving public safety through innovative solutions.
          </Text>
          
          <View style={styles.contactCard}>
            <Text style={styles.contactTitle}>Contact Information</Text>
            <TouchableOpacity 
              style={styles.contactItem}
              onPress={() => handleLinkPress('mailto:<EMAIL>')}
            >
              <Ionicons name="mail" size={20} color={config.COLORS.primary} />
              <Text style={styles.contactText}><EMAIL></Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.contactItem}
              onPress={() => handleLinkPress('tel:+18002537801')}
            >
              <Ionicons name="call" size={20} color={config.COLORS.primary} />
              <Text style={styles.contactText}>1-800-ALERT-01</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.contactItem}
              onPress={() => handleLinkPress('https://www.alertcomm.com')}
            >
              <Ionicons name="globe" size={20} color={config.COLORS.primary} />
              <Text style={styles.contactText}>www.alertcomm.com</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Legal */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Legal & Compliance</Text>
          <Text style={styles.paragraph}>
            AlertComm complies with all applicable emergency communication regulations and 
            data protection standards including HIPAA, FEMA guidelines, and state emergency 
            response protocols.
          </Text>
        </View>

        {/* Acknowledgments */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Acknowledgments</Text>
          <Text style={styles.paragraph}>
            Special thanks to the first responders, emergency personnel, and volunteers who 
            provided valuable feedback during the development of this platform. Your dedication 
            to public safety inspires our work every day.
          </Text>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            © 2024 AlertComm. All rights reserved.
          </Text>
          <Text style={styles.footerSubtext}>
            Proudly supporting emergency responders worldwide
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
    backgroundColor: config.COLORS.surface,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: config.COLORS.text,
  },
  placeholder: {
    width: 32,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  logoSection: {
    alignItems: 'center',
    padding: 32,
    backgroundColor: config.COLORS.surface,
  },
  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: config.COLORS.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: config.COLORS.text,
    marginBottom: 8,
  },
  appTagline: {
    fontSize: 16,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: 8,
  },
  version: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    fontWeight: '500',
  },
  section: {
    padding: 16,
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 12,
  },
  paragraph: {
    fontSize: 16,
    color: config.COLORS.text,
    lineHeight: 24,
    marginBottom: 8,
  },
  bulletPoint: {
    fontSize: 16,
    color: config.COLORS.text,
    lineHeight: 24,
    marginBottom: 4,
    marginLeft: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
    padding: 12,
    backgroundColor: config.COLORS.surface,
    borderRadius: 8,
  },
  featureText: {
    flex: 1,
    marginLeft: 12,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    lineHeight: 20,
  },
  contactCard: {
    backgroundColor: config.COLORS.surface,
    padding: 16,
    borderRadius: 8,
    marginTop: 12,
  },
  contactTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 12,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  contactText: {
    fontSize: 16,
    color: config.COLORS.primary,
    marginLeft: 12,
  },
  footer: {
    alignItems: 'center',
    padding: 24,
    backgroundColor: config.COLORS.surface,
    marginTop: 16,
  },
  footerText: {
    fontSize: 16,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 4,
  },
  footerSubtext: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    fontStyle: 'italic',
  },
});

export default AboutScreen;
