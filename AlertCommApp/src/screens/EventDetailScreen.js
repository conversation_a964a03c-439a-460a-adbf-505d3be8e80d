import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import EventMapView from '../components/EventMapView';
import apiService from '../services/api';
import socketService from '../services/socket';
import config from '../config/config';

const EventDetailScreen = ({ route, navigation }) => {
  const { eventId } = route.params;
  const { user } = useAuth();
  const [event, setEvent] = useState(null);
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [userTask, setUserTask] = useState(null);

  useEffect(() => {
    loadEventDetails();
    setupSocketListeners();
    
    return () => {
      socketService.off('statusUpdate', handleStatusUpdate);
    };
  }, [eventId]);

  const loadEventDetails = async () => {
    try {
      const [eventData, tasksData] = await Promise.all([
        apiService.getEvent(eventId),
        apiService.getEventTasks(eventId),
      ]);
      
      setEvent(eventData);
      setTasks(tasksData);
      
      // Find user's task for this event
      const myTask = tasksData.find(task => task.assigned_to === user.id);
      setUserTask(myTask);
      
      // Join event room for real-time updates
      socketService.joinEventRoom(eventId);
      
    } catch (error) {
      console.error('Error loading event details:', error);
      Alert.alert('Error', 'Failed to load event details');
    } finally {
      setLoading(false);
    }
  };

  const setupSocketListeners = () => {
    socketService.on('statusUpdate', handleStatusUpdate);
  };

  const handleStatusUpdate = (update) => {
    if (update.eventId === eventId) {
      setTasks(prev => prev.map(task => 
        task.assigned_to === update.userId 
          ? { ...task, status: update.status }
          : task
      ));
      
      if (update.userId === user.id) {
        setUserTask(prev => prev ? { ...prev, status: update.status } : null);
      }
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadEventDetails();
    setRefreshing(false);
  };

  const updateTaskStatus = async (newStatus) => {
    if (!userTask) return;
    
    try {
      await apiService.updateTaskStatus(userTask.id, newStatus);
      setUserTask(prev => ({ ...prev, status: newStatus }));
      socketService.sendStatusUpdate(eventId, user.id, newStatus);
    } catch (error) {
      Alert.alert('Error', 'Failed to update status');
    }
  };

  const getUrgencyColor = (urgency) => {
    return config.URGENCY_COLORS[urgency] || config.COLORS.textSecondary;
  };

  const getStatusColor = (status) => {
    return config.STATUS_COLORS[status] || config.COLORS.disabled;
  };

  const renderEventHeader = () => (
    <View style={styles.headerCard}>
      <View style={styles.eventTitleContainer}>
        <Text style={styles.eventTitle}>{event.title}</Text>
        <View style={styles.badges}>
          <View style={[styles.urgencyBadge, { backgroundColor: getUrgencyColor(event.urgency) }]}>
            <Text style={styles.badgeText}>{event.urgency}</Text>
          </View>
          <View style={[styles.moduleBadge, { backgroundColor: config.COLORS.primary }]}>
            <Text style={styles.badgeText}>{event.module || 'General'}</Text>
          </View>
        </View>
      </View>
      
      {event.info && (
        <Text style={styles.eventInfo}>{event.info}</Text>
      )}
      
      {event.description && (
        <Text style={styles.eventDescription}>{event.description}</Text>
      )}
      
      {event.location && (
        <View style={styles.locationContainer}>
          <Ionicons name="location" size={16} color={config.COLORS.primary} />
          <Text style={styles.locationText}>
            {event.location.commonName || event.location.address}
          </Text>
        </View>
      )}
      
      <Text style={styles.eventTime}>
        Created: {new Date(event.created_at).toLocaleString()}
      </Text>
    </View>
  );

  const renderEventMap = () => {
    // Get responders with location data
    const respondersWithLocation = (tasks || [])
      .filter(task => task.assigned_to && task.latitude && task.longitude)
      .map(task => ({
        id: task.assigned_to,
        latitude: task.latitude,
        longitude: task.longitude,
        status: task.status,
        first_name: task.first_name || 'Unknown',
        last_name: task.last_name || 'Responder',
        job_role: task.job_role || 'Staff',
        role: task.role || 'staff'
      }));

    // Add some test location data if event doesn't have coordinates
    const eventWithLocation = {
      ...event,
      latitude: event?.latitude || '37.7749', // Default to San Francisco
      longitude: event?.longitude || '-122.4194'
    };

    return (
      <View style={styles.mapCard}>
        <Text style={styles.cardTitle}>Event Location & Responders</Text>



        <EventMapView
          event={eventWithLocation}
          responders={respondersWithLocation}
          style={styles.eventMap}
          onResponderPress={(responder) => {
            Alert.alert(
              `${responder.first_name} ${responder.last_name}`,
              `Role: ${responder.job_role}\nStatus: ${responder.status}`,
              [{ text: 'OK' }]
            );
          }}
        />
        <View style={styles.mapInfo}>
          <Text style={styles.mapInfoText}>
            📍 Location: {event?.location?.address || event?.location || 'Event location'}
          </Text>
          <Text style={styles.mapInfoText}>
            👥 {respondersWithLocation.length} responder{respondersWithLocation.length !== 1 ? 's' : ''} tracked
          </Text>
        </View>
      </View>
    );
  };

  const renderEventChat = () => {
    // Get assigned responders for this event
    const assignedResponders = (tasks || []).filter(task => task.assigned_to);

    return (
      <View style={styles.chatCard}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>Event Team Chat</Text>
          <View style={styles.chatParticipants}>
            <Ionicons name="people" size={16} color={config.COLORS.textSecondary} />
            <Text style={styles.participantsText}>{assignedResponders.length} members</Text>
          </View>
        </View>

        <View style={styles.chatDescription}>
          <Text style={styles.chatDescriptionText}>
            Private chat for responders assigned to this event. Only team members can see these messages.
          </Text>
        </View>

        <View style={styles.chatPreview}>
          <View style={styles.chatMessage}>
            <View style={styles.chatAvatar}>
              <Ionicons name="person" size={16} color="#fff" />
            </View>
            <View style={styles.chatContent}>
              <Text style={styles.chatSender}>Team Lead</Text>
              <Text style={styles.chatText}>All units report status - ETA to scene?</Text>
              <Text style={styles.chatTime}>3 minutes ago</Text>
            </View>
          </View>

          <View style={styles.chatMessage}>
            <View style={styles.chatAvatar}>
              <Ionicons name="person" size={16} color="#fff" />
            </View>
            <View style={styles.chatContent}>
              <Text style={styles.chatSender}>Responder 1</Text>
              <Text style={styles.chatText}>En route, ETA 5 minutes</Text>
              <Text style={styles.chatTime}>2 minutes ago</Text>
            </View>
          </View>

          <View style={styles.chatMessage}>
            <View style={styles.chatAvatar}>
              <Ionicons name="person" size={16} color="#fff" />
            </View>
            <View style={styles.chatContent}>
              <Text style={styles.chatSender}>Responder 2</Text>
              <Text style={styles.chatText}>On scene, setting up perimeter</Text>
              <Text style={styles.chatTime}>1 minute ago</Text>
            </View>
          </View>
        </View>

        <TouchableOpacity
          style={styles.joinChatButton}
          onPress={() => {
            if (navigation) {
              navigation.navigate('Chat', { eventId, eventTitle: event?.title });
            } else {
              Alert.alert('Event Chat', `Opening team chat for: ${event?.title || 'this event'}`);
            }
          }}
        >
          <Ionicons name="chatbubbles" size={20} color="#fff" />
          <Text style={styles.joinChatText}>Open Event Team Chat</Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderEventDocuments = () => {
    const sampleDocuments = [
      { id: 1, name: 'Emergency Response Plan.pdf', type: 'pdf', size: '2.3 MB', uploadedBy: 'Commander', uploadedAt: '10 minutes ago' },
      { id: 2, name: 'Site Photos.jpg', type: 'image', size: '1.8 MB', uploadedBy: 'Field Team', uploadedAt: '15 minutes ago' },
      { id: 3, name: 'Resource Allocation.xlsx', type: 'excel', size: '456 KB', uploadedBy: 'Logistics', uploadedAt: '20 minutes ago' },
    ];

    const getDocumentIcon = (type) => {
      switch (type) {
        case 'pdf': return 'document-text';
        case 'image': return 'image';
        case 'excel': return 'grid';
        default: return 'document';
      }
    };

    return (
      <View style={styles.documentsCard}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>Event Documents</Text>
          <TouchableOpacity style={styles.uploadButton}>
            <Ionicons name="cloud-upload" size={16} color={config.COLORS.primary} />
            <Text style={styles.uploadText}>Upload</Text>
          </TouchableOpacity>
        </View>

        {sampleDocuments.map((doc) => (
          <TouchableOpacity key={doc.id} style={styles.documentItem}>
            <View style={styles.documentIcon}>
              <Ionicons name={getDocumentIcon(doc.type)} size={24} color={config.COLORS.primary} />
            </View>
            <View style={styles.documentInfo}>
              <Text style={styles.documentName}>{doc.name}</Text>
              <Text style={styles.documentMeta}>
                {doc.size} • {doc.uploadedBy} • {doc.uploadedAt}
              </Text>
            </View>
            <TouchableOpacity style={styles.documentAction}>
              <Ionicons name="download" size={20} color={config.COLORS.textSecondary} />
            </TouchableOpacity>
          </TouchableOpacity>
        ))}

        <TouchableOpacity style={styles.viewAllDocumentsButton}>
          <Text style={styles.viewAllDocumentsText}>View All Documents ({sampleDocuments.length})</Text>
          <Ionicons name="chevron-forward" size={16} color={config.COLORS.primary} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderMyStatus = () => {
    if (!userTask) return null;
    
    return (
      <View style={styles.statusCard}>
        <Text style={styles.cardTitle}>My Response Status</Text>
        <View style={styles.currentStatusContainer}>
          <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(userTask.status) }]} />
          <Text style={styles.currentStatusText}>{userTask.status.toUpperCase()}</Text>
        </View>
        
        <View style={styles.statusButtons}>
          {config.RESPONSE_STATUS.map((status) => (
            <TouchableOpacity
              key={status.value}
              style={[
                styles.statusButton,
                { backgroundColor: status.color },
                userTask.status === status.value && styles.activeStatusButton
              ]}
              onPress={() => updateTaskStatus(status.value)}
            >
              <Text style={styles.statusButtonText}>{status.label}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderTeamStatus = () => (
    <View style={styles.teamCard}>
      <Text style={styles.cardTitle}>Team Status</Text>
      {tasks.length === 0 ? (
        <Text style={styles.noTasksText}>No team members assigned</Text>
      ) : (
        tasks.map((task) => (
          <View key={task.id} style={styles.teamMemberRow}>
            <View style={styles.memberInfo}>
              <Text style={styles.memberName}>{task.username || `User ${task.assigned_to}`}</Text>
              <Text style={styles.memberRole}>{task.role || 'Staff'}</Text>
            </View>
            <View style={styles.memberStatus}>
              <View style={[styles.statusIndicator, { backgroundColor: getStatusColor(task.status) }]} />
              <Text style={styles.memberStatusText}>{task.status}</Text>
            </View>
          </View>
        ))
      )}
    </View>
  );

  const renderQuickActions = () => (
    <View style={styles.actionsCard}>
      <Text style={styles.cardTitle}>Quick Actions</Text>
      <View style={styles.actionsGrid}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={() => navigation.navigate('Chat', { eventId })}
        >
          <Ionicons name="chatbubbles" size={24} color={config.COLORS.primary} />
          <Text style={styles.actionText}>Event Chat</Text>
        </TouchableOpacity>
        
        {userTask && (
          <>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => updateTaskStatus('acknowledged')}
            >
              <Ionicons name="checkmark-circle" size={24} color={config.COLORS.success} />
              <Text style={styles.actionText}>Acknowledge</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => updateTaskStatus('enroute')}
            >
              <Ionicons name="car" size={24} color={config.COLORS.warning} />
              <Text style={styles.actionText}>En Route</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => updateTaskStatus('on_scene')}
            >
              <Ionicons name="location" size={24} color={config.COLORS.primary} />
              <Text style={styles.actionText}>On Scene</Text>
            </TouchableOpacity>
          </>
        )}
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading event details...</Text>
      </View>
    );
  }

  if (!event) {
    return (
      <View style={styles.errorContainer}>
        <Text>Event not found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={true}
        keyboardShouldPersistTaps="handled"
        removeClippedSubviews={false}
        scrollEventThrottle={16}
      >
        {renderEventHeader()}
        {renderEventMap()}
        {renderEventChat()}
        {renderEventDocuments()}
        {renderMyStatus()}
        {renderTeamStatus()}
        {renderQuickActions()}
      </ScrollView>

      {/* Floating Chat Button for this Event */}
      <TouchableOpacity
        style={styles.floatingChatButton}
        onPress={() => {
          if (navigation) {
            navigation.navigate('Chat', { eventId, eventTitle: event?.title });
          }
        }}
      >
        <Ionicons name="chatbubbles" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: config.COLORS.background,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  floatingChatButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: config.COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  eventTitleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  eventTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: config.COLORS.text,
    flex: 1,
    marginRight: 12,
  },
  badges: {
    flexDirection: 'row',
    gap: 8,
  },
  urgencyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  moduleBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  badgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
    textTransform: 'uppercase',
  },
  eventInfo: {
    fontSize: 16,
    color: config.COLORS.text,
    marginBottom: 8,
    lineHeight: 22,
  },
  eventDescription: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    marginBottom: 12,
    lineHeight: 20,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  locationText: {
    fontSize: 14,
    color: config.COLORS.text,
    marginLeft: 8,
    flex: 1,
  },
  eventTime: {
    fontSize: 12,
    color: config.COLORS.disabled,
  },
  statusCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: config.COLORS.text,
    marginBottom: 12,
  },
  currentStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  currentStatusText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: config.COLORS.text,
  },
  statusButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  statusButton: {
    flex: 1,
    minWidth: '45%',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  activeStatusButton: {
    borderWidth: 2,
    borderColor: config.COLORS.text,
  },
  statusButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  teamCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  noTasksText: {
    textAlign: 'center',
    color: config.COLORS.textSecondary,
    fontStyle: 'italic',
    padding: 20,
  },
  teamMemberRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: config.COLORS.text,
  },
  memberRole: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
  },
  memberStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  memberStatusText: {
    fontSize: 12,
    color: config.COLORS.text,
    marginLeft: 4,
  },
  actionsCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: '48%',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    backgroundColor: config.COLORS.background,
    marginBottom: 8,
  },
  actionText: {
    marginTop: 8,
    fontSize: 12,
    color: config.COLORS.text,
    textAlign: 'center',
  },
  mapCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginTop: 0,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  eventMap: {
    marginTop: 12,
  },
  noRespondersText: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    textAlign: 'center',
    marginTop: 12,
    fontStyle: 'italic',
  },
  mapInfo: {
    marginTop: 12,
    padding: 12,
    backgroundColor: config.COLORS.background,
    borderRadius: 8,
  },
  mapInfoText: {
    fontSize: 14,
    color: config.COLORS.textSecondary,
    marginBottom: 4,
  },
  debugInfo: {
    backgroundColor: '#f0f0f0',
    padding: 8,
    borderRadius: 4,
    marginBottom: 12,
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
  },
  // Chat Card Styles
  chatCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginTop: 0,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewAllText: {
    fontSize: 14,
    color: config.COLORS.primary,
    marginRight: 4,
  },
  chatParticipants: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  participantsText: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
    marginLeft: 4,
  },
  chatDescription: {
    backgroundColor: config.COLORS.background,
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  chatDescriptionText: {
    fontSize: 13,
    color: config.COLORS.textSecondary,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  chatPreview: {
    marginBottom: 16,
  },
  chatMessage: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  chatAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: config.COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  chatContent: {
    flex: 1,
  },
  chatSender: {
    fontSize: 14,
    fontWeight: '600',
    color: config.COLORS.text,
    marginBottom: 2,
  },
  chatText: {
    fontSize: 14,
    color: config.COLORS.text,
    marginBottom: 2,
  },
  chatTime: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
  },
  joinChatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: config.COLORS.primary,
    borderRadius: 8,
    padding: 12,
  },
  joinChatText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  // Documents Card Styles
  documentsCard: {
    backgroundColor: config.COLORS.surface,
    margin: 16,
    marginTop: 0,
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: config.COLORS.primary + '20',
    borderRadius: 6,
    padding: 8,
  },
  uploadText: {
    fontSize: 14,
    color: config.COLORS.primary,
    marginLeft: 4,
    fontWeight: '500',
  },
  documentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: config.COLORS.border,
  },
  documentIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: config.COLORS.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: 14,
    fontWeight: '500',
    color: config.COLORS.text,
    marginBottom: 2,
  },
  documentMeta: {
    fontSize: 12,
    color: config.COLORS.textSecondary,
  },
  documentAction: {
    padding: 8,
  },
  viewAllDocumentsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
    padding: 12,
    backgroundColor: config.COLORS.background,
    borderRadius: 8,
  },
  viewAllDocumentsText: {
    fontSize: 14,
    color: config.COLORS.primary,
    fontWeight: '500',
    marginRight: 4,
  },
});

export default EventDetailScreen;
