import React, { createContext, useContext, useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { jwtDecode } from 'jwt-decode';
import config from '../config/config';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(true);

  // Check for stored token on app start
  useEffect(() => {
    checkStoredToken();
  }, []);

  const checkStoredToken = async () => {
    try {
      const storedToken = await AsyncStorage.getItem('authToken');
      if (storedToken) {
        const decoded = jwtDecode(storedToken);
        
        // Check if token is expired
        if (decoded.exp * 1000 > Date.now()) {
          setToken(storedToken);
          setUser({
            id: decoded.id,
            username: decoded.username,
            role: decoded.role,
          });
        } else {
          // Token expired, remove it
          await AsyncStorage.removeItem('authToken');
        }
      }
    } catch (error) {
      console.error('Error checking stored token:', error);
      await AsyncStorage.removeItem('authToken');
    } finally {
      setLoading(false);
    }
  };

  const login = async (username, password) => {
    try {
      const response = await fetch(`${config.API_BASE_URL}${config.ENDPOINTS.LOGIN}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Login failed');
      }

      const data = await response.json();
      const decoded = jwtDecode(data.token);

      // Store token
      await AsyncStorage.setItem('authToken', data.token);

      setToken(data.token);
      setUser({
        id: decoded.id,
        username: decoded.username,
        role: decoded.role,
      });

      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: error.message };
    }
  };

  const logout = async () => {
    try {
      console.log('🚪 Logout initiated...');
      console.log('Current token before logout:', !!token);
      console.log('Current user before logout:', user?.username);

      // Clear AsyncStorage first
      await AsyncStorage.removeItem('authToken');
      console.log('✅ Token removed from AsyncStorage');

      // Clear all possible storage keys (in case there are others)
      await AsyncStorage.multiRemove(['authToken', 'userProfile', 'userPreferences']);
      console.log('✅ All storage cleared');

      // Clear state immediately
      setToken(null);
      setUser(null);
      setLoading(false);

      console.log('✅ Auth state cleared');
      console.log('isAuthenticated should now be:', false);

      // Force state update with a small delay
      await new Promise(resolve => setTimeout(resolve, 200));

      // For web platform, force a page reload to ensure clean state
      if (typeof window !== 'undefined') {
        console.log('🌐 Web platform detected - forcing page reload...');
        setTimeout(() => {
          window.location.href = '/';
        }, 300);
      }

      console.log('🎉 Logout completed successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Logout error:', error);
      return { success: false, error: error.message };
    }
  };

  const value = {
    user,
    token,
    loading,
    login,
    logout,
    isAuthenticated: !!token,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
