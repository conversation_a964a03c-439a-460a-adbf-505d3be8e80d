import AsyncStorage from '@react-native-async-storage/async-storage';
import config from '../config/config';

class ApiService {
  constructor() {
    this.baseURL = config.API_BASE_URL;
  }

  async getAuthHeaders() {
    const token = await AsyncStorage.getItem('authToken');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  async request(endpoint, options = {}) {
    try {
      const headers = await this.getAuthHeaders();
      const url = `${this.baseURL}${endpoint}`;
      
      const response = await fetch(url, {
        headers,
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Authentication
  async login(username, password) {
    return this.request(config.ENDPOINTS.LOGIN, {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });
  }

  // Events
  async getEvents() {
    return this.request(config.ENDPOINTS.EVENTS);
  }

  async getEvent(eventId) {
    return this.request(`${config.ENDPOINTS.EVENTS}/${eventId}`);
  }

  async getEventTasks(eventId) {
    return this.request(`${config.ENDPOINTS.EVENTS}/${eventId}/tasks`);
  }

  // Tasks
  async updateTaskStatus(taskId, status, location = null) {
    const body = { status };
    if (location) {
      body.location = location;
    }
    
    return this.request(`${config.ENDPOINTS.TASKS}/${taskId}/status`, {
      method: 'PUT',
      body: JSON.stringify(body),
    });
  }

  // Chat
  async getChatMessages(eventId) {
    return this.request(`${config.ENDPOINTS.EVENTS}/${eventId}/chat`);
  }

  async sendChatMessage(eventId, message) {
    return this.request(`${config.ENDPOINTS.EVENTS}/${eventId}/chat`, {
      method: 'POST',
      body: JSON.stringify({ text: message }),
    });
  }

  // Location
  async updateLocation(latitude, longitude) {
    return this.request(config.ENDPOINTS.LOCATION, {
      method: 'PUT',
      body: JSON.stringify({ latitude, longitude }),
    });
  }

  // Status
  async updateResponderStatus(status) {
    return this.request(config.ENDPOINTS.STATUS, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  }

  // Notifications
  async getNotifications() {
    return this.request(config.ENDPOINTS.NOTIFICATIONS);
  }

  async markNotificationRead(notificationId) {
    return this.request(`${config.ENDPOINTS.NOTIFICATIONS}/${notificationId}/read`, {
      method: 'PUT',
    });
  }



  // Event Management
  async createEvent(eventData) {
    return this.request('/events', {
      method: 'POST',
      body: JSON.stringify(eventData),
    });
  }

  // Task Management
  async createTask(taskData) {
    return this.request('/tasks', {
      method: 'POST',
      body: JSON.stringify(taskData),
    });
  }

  // Notification Management
  async createNotification(notificationData) {
    return this.request('/notifications', {
      method: 'POST',
      body: JSON.stringify(notificationData),
    });
  }

  // User Management
  async getUsers() {
    return this.request('/users');
  }

  async updateUserStatus(userId, status) {
    return this.request(`/users/${userId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  }

  async updateUserRole(userId, role) {
    return this.request(`/users/${userId}/role`, {
      method: 'PUT',
      body: JSON.stringify({ role }),
    });
  }

  async createUser(userData) {
    return this.request('/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async deleteUser(userId) {
    return this.request(`/users/${userId}`, {
      method: 'DELETE',
    });
  }

  async getUserProfile(userId = null) {
    const endpoint = userId ? `/users/${userId}/profile` : '/profile';
    return this.request(endpoint);
  }

  async updateUserProfile(profileData) {
    return this.request('/profile', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });
  }
}

export default new ApiService();
